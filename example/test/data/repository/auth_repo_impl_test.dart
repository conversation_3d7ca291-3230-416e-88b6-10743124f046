// import 'package:flutter_test/flutter_test.dart';
// import 'package:mockito/mockito.dart';
// import 'package:example/data/repository/auth_repo_impl.dart';
// import 'package:example/data/model/auth/request/auth_check_email_request.dart';
// import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
// import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';

// import '../../helpers/test_helper.mocks.dart';
// import '../../helpers/test_constants.dart';

// void main() {
//   group('AuthRepositoryImpl', () {
//     late MockAuthService mockAuthService;
//     late AuthRepositoryImpl authRepository;

//     setUp(() {
//       mockAuthService = MockAuthService();
//       authRepository = AuthRepositoryImpl(mockAuthService);
//     });

//     group('checkEmail', () {
//       test('should return success response when service call succeeds', () async {
//         // Arrange
//         final request = TestConstants.testAuthCheckEmailRequest;
//         final expectedResponse = TestConstants.testSuccessApiResponse;
        
//         when(mockAuthService.checkEmail(checkEmailRequest: request))
//             .thenAnswer((_) async => expectedResponse);

//         // Act
//         final result = await authRepository.checkEmail(checkEmailRequest: request);

//         // Assert
//         expect(result, expectedResponse);
//         expect(result.status, 'success');
//         expect(result.data, isNotNull);
//         expect(result.data.userId, TestConstants.testUserId);
//         expect(result.data.newDomain, TestConstants.testNewDomain);
//         expect(result.data.salt, TestConstants.testSalt);
//       });

//       test('should return error response when service call fails', () async {
//         // Arrange
//         final request = TestConstants.testAuthCheckEmailRequest;
//         final expectedResponse = TestConstants.testErrorApiResponse;
        
//         when(mockAuthService.checkEmail(checkEmailRequest: request))
//             .thenAnswer((_) async => expectedResponse);

//         // Act
//         final result = await authRepository.checkEmail(checkEmailRequest: request);

//         // Assert
//         expect(result, expectedResponse);
//         expect(result.status, 'error');
//         expect(result.data, isNotNull);
//         expect(result.data.userId, -1);
//       });

//       test('should propagate exception when service throws', () async {
//         // Arrange
//         final request = TestConstants.testAuthCheckEmailRequest;
//         final exception = Exception('Network error');
        
//         when(mockAuthService.checkEmail(checkEmailRequest: request))
//             .thenThrow(exception);

//         // Act & Assert
//         expect(
//           () async => await authRepository.checkEmail(checkEmailRequest: request),
//           throwsA(isA<Exception>()),
//         );
//       });

//       test('should call service with correct parameters', () async {
//         // Arrange
//         final request = TestConstants.testAuthCheckEmailRequest;
//         final expectedResponse = TestConstants.testSuccessApiResponse;
        
//         when(mockAuthService.checkEmail(checkEmailRequest: request))
//             .thenAnswer((_) async => expectedResponse);

//         // Act
//         await authRepository.checkEmail(checkEmailRequest: request);

//         // Assert
//         verify(mockAuthService.checkEmail(checkEmailRequest: request)).called(1);
//       });

//       test('should handle multiple concurrent calls', () async {
//         // Arrange
//         final request1 = AuthCheckEmailRequest('<EMAIL>', '+84111111111');
//         final request2 = AuthCheckEmailRequest('<EMAIL>', '+84222222222');
//         final response1 = ApiResponseV2<AuthCheckMailResponse>(
//           status: 'success',
//           data: AuthCheckMailResponse(userId: 1),
//         );
//         final response2 = ApiResponseV2<AuthCheckMailResponse>(
//           status: 'success',
//           data: AuthCheckMailResponse(userId: 2),
//         );
        
//         when(mockAuthService.checkEmail(checkEmailRequest: request1))
//             .thenAnswer((_) async => response1);
//         when(mockAuthService.checkEmail(checkEmailRequest: request2))
//             .thenAnswer((_) async => response2);

//         // Act
//         final futures = [
//           authRepository.checkEmail(checkEmailRequest: request1),
//           authRepository.checkEmail(checkEmailRequest: request2),
//         ];
//         final results = await Future.wait(futures);

//         // Assert
//         expect(results[0].data.userId, 1);
//         expect(results[1].data.userId, 2);
//         verify(mockAuthService.checkEmail(checkEmailRequest: request1)).called(1);
//         verify(mockAuthService.checkEmail(checkEmailRequest: request2)).called(1);
//       });

//       test('should handle empty response data', () async {
//         // Arrange
//         final request = TestConstants.testAuthCheckEmailRequest;
//         final responseWithEmptyData = ApiResponseV2<AuthCheckMailResponse>(
//           status: 'no_content',
//           data: AuthCheckMailResponse(userId: 0),
//         );

//         when(mockAuthService.checkEmail(checkEmailRequest: request))
//             .thenAnswer((_) async => responseWithEmptyData);

//         // Act
//         final result = await authRepository.checkEmail(checkEmailRequest: request);

//         // Assert
//         expect(result.status, 'no_content');
//         expect(result.data, isNotNull);
//         expect(result.data.userId, 0);
//       });
//     });
//   });
// }

// class MockAuthService {
// }
