import 'package:flutter_test/flutter_test.dart';
import 'package:example/mapper/entity/auth/auth_entity_mapper.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:example/domain/entity/auth.entity.dart';

void main() {
  group('AuthCheckMailMapper', () {
    late AuthCheckMailMapper mapper;

    setUp(() {
      mapper = const AuthCheckMailMapper();
    });

    group('AuthCheckMailResponse to AuthCheckMailEntity', () {
      test('should map response to entity correctly', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 123,
          newDomain: true,
          salt: 'test_salt',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity, isA<AuthCheckMailEntity>());
        expect(entity.userId, 123);
        expect(entity.newDomain, true);
        // salt should be ignored according to mapper configuration
      });

      test('should map response with different values correctly', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 456,
          newDomain: false,
          salt: 'another_salt',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 456);
        expect(entity.newDomain, false);
      });

      test('should map response with zero userId', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 0,
          newDomain: false,
          salt: '',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 0);
        expect(entity.newDomain, false);
      });

      test('should map response with large userId', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 999999999,
          newDomain: true,
          salt: 'large_user_salt',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 999999999);
        expect(entity.newDomain, true);
      });
    });

    group('Mapper Properties', () {
      test('should be const constructor', () {
        // Arrange & Act
        const mapper1 = AuthCheckMailMapper();
        const mapper2 = AuthCheckMailMapper();

        // Assert
        expect(mapper1, equals(mapper2));
        expect(identical(mapper1, mapper2), false); // Different instances
      });

      test('should have correct type', () {
        // Assert
        expect(mapper, isA<AuthCheckMailMapper>());
      });
    });

    group('Edge Cases', () {
      test('should handle multiple conversions consistently', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 789,
          newDomain: true,
          salt: 'consistent_salt',
        );

        // Act
        final entity1 = mapper.convert(response);
        final entity2 = mapper.convert(response);

        // Assert
        expect(entity1.userId, entity2.userId);
        expect(entity1.newDomain, entity2.newDomain);
      });

      test('should handle conversion with empty salt', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 100,
          newDomain: false,
          salt: '',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 100);
        expect(entity.newDomain, false);
      });

      test('should handle conversion with null-like values', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 0,
          newDomain: false,
          salt: '',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 0);
        expect(entity.newDomain, false);
      });
    });

    group('Performance', () {
      test('should handle rapid conversions efficiently', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 555,
          newDomain: true,
          salt: 'performance_salt',
        );

        final stopwatch = Stopwatch()..start();

        // Act
        for (int i = 0; i < 1000; i++) {
          mapper.convert(response);
        }

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should handle large batch conversions', () {
        // Arrange
        final responses = List.generate(100, (index) => AuthCheckMailResponse(
          userId: index,
          newDomain: index % 2 == 0,
          salt: 'salt_$index',
        ));

        final stopwatch = Stopwatch()..start();

        // Act
        final entities = responses.map((response) => mapper.convert(response)).toList();

        stopwatch.stop();

        // Assert
        expect(entities.length, 100);
        expect(stopwatch.elapsedMilliseconds, lessThan(50));
        
        // Verify first and last entities
        expect(entities.first.userId, 0);
        expect(entities.last.userId, 99);
      });
    });

    group('Type Safety', () {
      test('should maintain type safety during conversion', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 777,
          newDomain: true,
          salt: 'type_safe_salt',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity, isA<AuthCheckMailEntity>());
        expect(entity.userId, isA<int>());
        expect(entity.newDomain, isA<bool>());
      });

      test('should handle boolean values correctly', () {
        // Arrange
        final responseTrue = AuthCheckMailResponse(
          userId: 1,
          newDomain: true,
          salt: 'true_salt',
        );
        
        final responseFalse = AuthCheckMailResponse(
          userId: 2,
          newDomain: false,
          salt: 'false_salt',
        );

        // Act
        final entityTrue = mapper.convert(responseTrue);
        final entityFalse = mapper.convert(responseFalse);

        // Assert
        expect(entityTrue.newDomain, true);
        expect(entityFalse.newDomain, false);
      });
    });

    group('Immutability', () {
      test('should not modify original response during conversion', () {
        // Arrange
        final originalResponse = AuthCheckMailResponse(
          userId: 888,
          newDomain: true,
          salt: 'immutable_salt',
        );

        // Act
        final entity = mapper.convert(originalResponse);

        // Assert
        expect(originalResponse.userId, 888);
        expect(originalResponse.newDomain, true);
        expect(originalResponse.salt, 'immutable_salt');
        
        expect(entity.userId, 888);
        expect(entity.newDomain, true);
      });

      test('should create independent entity instances', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 999,
          newDomain: false,
          salt: 'independent_salt',
        );

        // Act
        final entity1 = mapper.convert(response);
        final entity2 = mapper.convert(response);

        // Assert
        expect(entity1.userId, entity2.userId);
        expect(entity1.newDomain, entity2.newDomain);
        expect(identical(entity1, entity2), false); // Different instances
      });
    });

    group('Field Mapping Verification', () {
      test('should ignore salt field as configured', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 111,
          newDomain: true,
          salt: 'should_be_ignored',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 111);
        expect(entity.newDomain, true);
        // Note: salt field is ignored in mapping configuration
        // so it should not be present in the entity
      });

      test('should map all non-ignored fields', () {
        // Arrange
        final response = AuthCheckMailResponse(
          userId: 222,
          newDomain: false,
          salt: 'any_salt_value',
        );

        // Act
        final entity = mapper.convert(response);

        // Assert
        expect(entity.userId, 222);
        expect(entity.newDomain, false);
        // Only userId and newDomain should be mapped
      });
    });
  });
}
