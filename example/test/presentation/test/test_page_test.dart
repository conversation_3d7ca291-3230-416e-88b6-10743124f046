import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:gp_core_v2/base/bloc/common/common.dart';
import 'package:example/presentation/test/test_page.dart';
import 'package:example/presentation/test/bloc/bloc.dart';

// Generate mocks
@GenerateMocks([TestBloc, CommonBloc])
import 'test_page_test.mocks.dart';

void main() {
  group('TestPage', () {
    late MockTestBloc mockTestBloc;
    late MockCommonBloc mockCommonBloc;

    setUp(() {
      mockTestBloc = MockTestBloc();
      mockCommonBloc = MockCommonBloc();

      // Setup default states
      when(mockTestBloc.state).thenReturn(const TestState('initial'));
      when(mockTestBloc.stream)
          .thenAnswer((_) => Stream.value(const TestState('initial')));

      when(mockCommonBloc.state).thenReturn(const CommonState());
      when(mockCommonBloc.stream)
          .thenAnswer((_) => Stream.value(const CommonState()));

      // Register mocks in GetIt
      if (GetIt.I.isRegistered<TestBloc>()) {
        GetIt.I.unregister<TestBloc>();
      }
      if (GetIt.I.isRegistered<CommonBloc>()) {
        GetIt.I.unregister<CommonBloc>();
      }

      GetIt.I.registerFactory<TestBloc>(() => mockTestBloc);
      GetIt.I.registerSingleton<CommonBloc>(mockCommonBloc);
    });

    tearDown(() {
      if (GetIt.I.isRegistered<TestBloc>()) {
        GetIt.I.unregister<TestBloc>();
      }
      if (GetIt.I.isRegistered<CommonBloc>()) {
        GetIt.I.unregister<CommonBloc>();
      }
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<CommonBloc>.value(value: mockCommonBloc),
          ],
          child: const TestPage(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render TestPage with all UI elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TestPage), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
        expect(find.byType(FloatingActionButton), findsOneWidget);
      });

      testWidgets('should display current state text',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Current State: initial'), findsOneWidget);
      });

      testWidgets('should display all action buttons',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('debugDumpRenderTree'), findsOneWidget);
        expect(find.text('TestEvent'), findsOneWidget);
        expect(find.text('TestCounterEvent'), findsOneWidget);
        expect(find.text('email check'), findsOneWidget);
        expect(find.text('Handle error'), findsOneWidget);
        expect(find.text('authCheckEmailRequestWithUseCase'), findsOneWidget);
        expect(find.text('Go to login page'), findsOneWidget);
      });

      testWidgets('should display common state texts',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('handle common state '), findsOneWidget);
        expect(find.text('handle common state with overrideErrorString'),
            findsOneWidget);
        expect(find.text('handle common state with listener'), findsOneWidget);
      });

      testWidgets('should display floating action button with developer icon',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(FloatingActionButton), findsOneWidget);
        expect(find.byIcon(Icons.developer_mode), findsOneWidget);
      });
    });

    group('State Management', () {
      testWidgets('should update state text when TestBloc state changes',
          (WidgetTester tester) async {
        // Arrange
        when(mockTestBloc.state).thenReturn(const TestState('updated'));
        when(mockTestBloc.stream)
            .thenAnswer((_) => Stream.value(const TestState('updated')));

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Current State: updated'), findsOneWidget);
      });

      testWidgets('should show loading indicator when CommonBloc is loading',
          (WidgetTester tester) async {
        // Arrange
        when(mockCommonBloc.state)
            .thenReturn(const CommonState(isLoading: true));
        when(mockCommonBloc.stream).thenAnswer(
            (_) => Stream.value(const CommonState(isLoading: true)));

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('CurrentState: NoLoading'), findsNothing);
      });

      testWidgets('should show no loading text when CommonBloc is not loading',
          (WidgetTester tester) async {
        // Arrange
        when(mockCommonBloc.state)
            .thenReturn(const CommonState(isLoading: false));
        when(mockCommonBloc.stream).thenAnswer(
            (_) => Stream.value(const CommonState(isLoading: false)));

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('CurrentState: NoLoading'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });
    });

    group('Button Interactions', () {
      testWidgets('should handle debugDumpRenderTree button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act & Assert - Should not throw
        expect(() async {
          await tester.tap(find.text('debugDumpRenderTree'));
          await tester.pumpAndSettle();
        }, returnsNormally);
      });

      testWidgets('should handle TestEvent button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('TestEvent'));
        await tester.pumpAndSettle();

        // Assert - Should call test method (behavior tested in behavior tests)
        expect(find.text('TestEvent'), findsOneWidget);
      });

      testWidgets('should handle TestCounterEvent button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('TestCounterEvent'));
        await tester.pumpAndSettle();

        // Assert - Should call testCounter method
        expect(find.text('TestCounterEvent'), findsOneWidget);
      });

      testWidgets('should handle email check button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('email check'));
        await tester.pumpAndSettle();

        // Assert - Should call authCheckEmailRequest method
        expect(find.text('email check'), findsOneWidget);
      });

      testWidgets('should handle error button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('Handle error'));
        await tester.pumpAndSettle();

        // Assert - Should call testError method
        expect(find.text('Handle error'), findsOneWidget);
      });

      testWidgets('should handle usecase button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('authCheckEmailRequestWithUseCase'));
        await tester.pumpAndSettle();

        // Assert - Should call authCheckEmailRequestWithUseCase method
        expect(find.text('authCheckEmailRequestWithUseCase'), findsOneWidget);
      });

      testWidgets('should handle login navigation button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('Go to login page'));
        await tester.pumpAndSettle();

        // Assert - Should call navigateToLogin method
        expect(find.text('Go to login page'), findsOneWidget);
      });

      testWidgets('should handle floating action button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act & Assert - Should not throw
        expect(() async {
          await tester.tap(find.byType(FloatingActionButton));
          await tester.pumpAndSettle();
        }, returnsNormally);
      });
    });

    group('BlocProvider Integration', () {
      testWidgets('should create TestBloc from GetIt',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        verify(mockTestBloc.state).called(greaterThan(0));
      });

      testWidgets('should provide TestBloc to child widgets',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final context = tester.element(find.byType(TestPage));
        expect(context.read<TestBloc>(), isNotNull);
      });

      testWidgets('should handle BlocBuilder updates',
          (WidgetTester tester) async {
        // Arrange
        final stateController = StreamController<TestState>();
        when(mockTestBloc.stream).thenAnswer((_) => stateController.stream);
        when(mockTestBloc.state).thenReturn(const TestState('initial'));

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        when(mockTestBloc.state).thenReturn(const TestState( 'changed'));
        stateController.add(const TestState('changed'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Current State: changed'), findsOneWidget);

        stateController.close();
      });
    });

    group('Layout and Styling', () {
      testWidgets('should have proper layout structure',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
        expect(scaffold.appBar, isNotNull);
        expect(scaffold.body, isNotNull);
        expect(scaffold.floatingActionButton, isNotNull);
      });

      testWidgets('should have scrollable content',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SingleChildScrollView), findsOneWidget);

        // Test scrolling
        await tester.drag(
            find.byType(SingleChildScrollView), const Offset(0, -200));
        await tester.pumpAndSettle();

        // Should still find the page
        expect(find.byType(TestPage), findsOneWidget);
      });

      testWidgets('should arrange buttons in column',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final column = tester.widget<Column>(find.byType(Column));
        expect(column.children.length, greaterThan(5));
      });
    });

    group('Mixin Integration', () {
      testWidgets('should integrate TestPageBehaviorMixin',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final testPage = tester.widget<TestPage>(find.byType(TestPage));
        expect(testPage, isA<TestPageBehaviorMixin>());
      });

      testWidgets('should integrate TalkerMixin', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final testPage = tester.widget<TestPage>(find.byType(TestPage));
        expect(testPage, isA<TalkerMixin>());
      });
    });

    group('Performance', () {
      testWidgets('should render efficiently', (WidgetTester tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should handle rapid state changes',
          (WidgetTester tester) async {
        // Arrange
        final stateController = StreamController<TestState>();
        when(mockTestBloc.stream).thenAnswer((_) => stateController.stream);
        when(mockTestBloc.state).thenReturn(const TestState(test: 'initial'));

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        final stopwatch = Stopwatch()..start();

        // Act - Rapid state changes
        for (int i = 0; i < 10; i++) {
          when(mockTestBloc.state).thenReturn(TestState(test: 'state$i'));
          stateController.add(TestState(test: 'state$i'));
          await tester.pump(const Duration(milliseconds: 10));
        }

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(500));

        stateController.close();
      });
    });

    group('Error Handling', () {
      testWidgets('should handle missing GetIt registration gracefully',
          (WidgetTester tester) async {
        // Arrange
        GetIt.I.unregister<TestBloc>();

        // Act & Assert
        expect(() async {
          await tester.pumpWidget(createTestWidget());
        }, throwsA(isA<AssertionError>()));
      });

      testWidgets('should handle bloc errors gracefully',
          (WidgetTester tester) async {
        // Arrange
        when(mockTestBloc.state).thenThrow(Exception('Test error'));

        // Act & Assert
        expect(() async {
          await tester.pumpWidget(createTestWidget());
        }, throwsException);
      });
    });

    group('Accessibility', () {
      testWidgets('should have accessible buttons',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final buttons = find.byType(TextButton);
        expect(buttons, findsWidgets);

        // All buttons should be tappable
        for (final button in buttons.evaluate()) {
          final widget = button.widget as TextButton;
          expect(widget.onPressed, isNotNull);
        }
      });

      testWidgets('should have accessible floating action button',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final fab = tester
            .widget<FloatingActionButton>(find.byType(FloatingActionButton));
        expect(fab.onPressed, isNotNull);
      });
    });
  });
}
