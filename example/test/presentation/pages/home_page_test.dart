import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:example/presentation/home/<USER>';

void main() {
  group('ExampleHomePage', () {
    Widget createTestWidget() {
      return const MaterialApp(
        home: ExampleHomePage(),
      );
    }

    testWidgets('should display home page with correct elements', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Verify AppBar
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);

      // Verify main content
      expect(find.byType(SafeArea), findsAtLeastNWidgets(1));
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(Padding), findsAtLeastNWidgets(1));
      expect(find.byType(Column), findsOneWidget);
    });

    testWidgets('should have correct layout structure', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Verify Scaffold
      expect(find.byType(Scaffold), findsOneWidget);

      // Verify SafeArea
      expect(find.byType(SafeArea), findsAtLeastNWidgets(1));

      // Verify SingleChildScrollView for scrollable content
      expect(find.byType(SingleChildScrollView), findsOneWidget);

      // Verify main Column layout
      expect(find.byType(Column), findsOneWidget);

      // Verify padding around content
      final paddingWidget = find.byWidgetPredicate(
        (widget) => widget is Padding && 
                   widget.padding == const EdgeInsets.all(16),
      );
      expect(paddingWidget, findsOneWidget);
    });

    testWidgets('should display all action buttons', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Count all ElevatedButtons
      expect(find.byType(ElevatedButton), findsNWidgets(11));

      // Verify specific button texts (using actual text from home_page.dart)
      expect(find.text('View user profile'), findsOneWidget);
      expect(find.text('Show normal snackbar'), findsOneWidget);
      expect(find.text('Show success snackbar'), findsOneWidget);
      expect(find.text('Show error snackbar'), findsOneWidget);
      expect(find.text('Show Dialog with one button'), findsOneWidget);
      expect(find.text('Show Dialog with two button'), findsOneWidget);
      expect(find.text('Show Dialog with two button vertical'), findsOneWidget);
      expect(find.text('Show Simple BottomSheet'), findsOneWidget);
      expect(find.text('Show BottomSheet with one button'), findsOneWidget);
      expect(find.text('Show BottomSheet with two button'), findsOneWidget);
      expect(find.text('Show BottomSheet with two button vertical'), findsOneWidget);
    });

    testWidgets('should handle button taps without errors', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Test each button tap (using actual text from home_page.dart)
      final buttonTexts = [
        'View user profile',
        'Show normal snackbar',
        'Show success snackbar',
        'Show error snackbar',
        'Show Dialog with one button',
        'Show Dialog with two button',
        'Show Dialog with two button vertical',
        'Show Simple BottomSheet',
        'Show BottomSheet with one button',
        'Show BottomSheet with two button',
        'Show BottomSheet with two button vertical',
      ];

      for (final buttonText in buttonTexts) {
        final button = find.text(buttonText);
        expect(button, findsOneWidget);
        
        await tester.tap(button);
        await tester.pump();
        
        // Should not crash after tapping
        expect(find.byType(ExampleHomePage), findsOneWidget);
      }
    });

    testWidgets('should have correct spacing between buttons', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find SizedBox widgets used for spacing
      final spacingWidgets = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.height == 16,
      );
      expect(spacingWidgets, findsAtLeastNWidgets(10)); // At least 10 spacing widgets
    });

    group('Widget Properties', () {
      testWidgets('should have correct AppBar properties', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.title, isA<Text>());
        
        final titleText = appBar.title as Text;
        expect(titleText.data, 'Home');
      });

      testWidgets('should have correct button properties', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final buttons = tester.widgetList<ElevatedButton>(find.byType(ElevatedButton));
        expect(buttons.length, 11);

        // Verify all buttons have onPressed callbacks
        for (final button in buttons) {
          expect(button.onPressed, isNotNull);
          expect(button.child, isA<Text>());
        }
      });

      testWidgets('should have correct padding properties', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final paddingWidget = tester.widget<Padding>(
          find.byWidgetPredicate(
            (widget) => widget is Padding && 
                       widget.padding == const EdgeInsets.all(16),
          ),
        );
        expect(paddingWidget.padding, const EdgeInsets.all(16));
      });
    });

    group('Scrolling Behavior', () {
      testWidgets('should be scrollable when content overflows', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Verify SingleChildScrollView exists
        expect(find.byType(SingleChildScrollView), findsOneWidget);

        // Try scrolling
        await tester.drag(find.byType(SingleChildScrollView), const Offset(0, -300));
        await tester.pumpAndSettle();

        // Should still find the home page
        expect(find.byType(ExampleHomePage), findsOneWidget);
      });

      testWidgets('should handle scroll gestures correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final scrollView = find.byType(SingleChildScrollView);
        
        // Scroll down
        await tester.drag(scrollView, const Offset(0, -100));
        await tester.pump();

        // Scroll up
        await tester.drag(scrollView, const Offset(0, 100));
        await tester.pump();

        // Should handle scrolling without errors
        expect(find.byType(ExampleHomePage), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have accessible elements', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Verify that all buttons are accessible
        expect(find.byType(ElevatedButton), findsNWidgets(11));

        // Verify semantic labels exist for all buttons
        final buttonTexts = [
          'View user profile',
          'Show normal snackbar',
          'Show success snackbar',
          'Show error snackbar',
          'Show Dialog with one button',
          'Show Dialog with two button',
          'Show Dialog with two button vertical',
          'Show Simple BottomSheet',
          'Show BottomSheet with one button',
          'Show BottomSheet with two button',
          'Show BottomSheet with two button vertical',
        ];

        for (final buttonText in buttonTexts) {
          expect(find.text(buttonText), findsOneWidget);
        }
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle rapid button taps', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final firstButton = find.text('Show normal snackbar');
        
        // Tap rapidly multiple times
        for (int i = 0; i < 5; i++) {
          await tester.tap(firstButton);
          await tester.pump(const Duration(milliseconds: 10));
        }

        // Should handle rapid taps without crashing
        expect(find.byType(ExampleHomePage), findsOneWidget);
      });

      testWidgets('should handle simultaneous button interactions', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final button1 = find.text('Show normal snackbar');
        final button2 = find.text('Show success snackbar');
        
        // Try to tap multiple buttons in quick succession
        await tester.tap(button1);
        await tester.tap(button2);
        await tester.pump();

        // Should handle multiple interactions without crashing
        expect(find.byType(ExampleHomePage), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should build efficiently', (tester) async {
        final stopwatch = Stopwatch()..start();
        
        await tester.pumpWidget(createTestWidget());
        
        stopwatch.stop();
        
        // Should build within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should handle multiple rebuilds efficiently', (tester) async {
        await tester.pumpWidget(createTestWidget());
        
        final stopwatch = Stopwatch()..start();
        
        // Trigger multiple rebuilds
        for (int i = 0; i < 10; i++) {
          await tester.pump();
        }
        
        stopwatch.stop();
        
        // Should handle rebuilds efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      testWidgets('should handle button interactions efficiently', (tester) async {
        await tester.pumpWidget(createTestWidget());
        
        final stopwatch = Stopwatch()..start();
        
        // Tap multiple buttons
        final buttons = find.byType(ElevatedButton);
        final buttonList = tester.widgetList<ElevatedButton>(buttons).take(5);
        
        for (final _ in buttonList) {
          await tester.tap(find.text('Show normal snackbar'));
          await tester.pump();
        }
        
        stopwatch.stop();
        
        // Should handle interactions efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Widget State', () {
      testWidgets('should maintain state during interactions', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Verify initial state
        expect(find.byType(ExampleHomePage), findsOneWidget);
        expect(find.byType(ElevatedButton), findsNWidgets(11));

        // Interact with buttons
        await tester.tap(find.text('Show normal snackbar'));
        await tester.pump();

        // State should be maintained
        expect(find.byType(ExampleHomePage), findsOneWidget);
        expect(find.byType(ElevatedButton), findsNWidgets(11));
      });

      testWidgets('should handle state changes correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Scroll and interact
        await tester.drag(find.byType(SingleChildScrollView), const Offset(0, -100));
        await tester.pump();
        
        await tester.tap(find.text('Show success snackbar'));
        await tester.pump();

        // Should maintain correct state
        expect(find.byType(ExampleHomePage), findsOneWidget);
      });
    });

    group('Layout Constraints', () {
      testWidgets('should handle different screen sizes', (tester) async {
        // Test with different screen sizes
        await tester.binding.setSurfaceSize(const Size(400, 600));
        await tester.pumpWidget(createTestWidget());
        expect(find.byType(ExampleHomePage), findsOneWidget);

        await tester.binding.setSurfaceSize(const Size(800, 1200));
        await tester.pumpWidget(createTestWidget());
        expect(find.byType(ExampleHomePage), findsOneWidget);

        // Reset to default size
        await tester.binding.setSurfaceSize(null);
      });
    });
  });
}
