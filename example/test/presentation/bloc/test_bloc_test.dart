import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:example/presentation/test/bloc/test_bloc.dart';
import 'package:example/presentation/test/bloc/test_event.dart';
import 'package:example/presentation/test/bloc/test_state.dart';


import '../../helpers/test_helper.mocks.dart';
import '../../helpers/test_constants.dart';

void main() {
  group('TestBloc', () {
    late MockAuthCheckMailUseCase mockAuthCheckMailUseCase;
    late TestBloc testBloc;

    setUp(() {
      mockAuthCheckMailUseCase = MockAuthCheckMailUseCase();
      testBloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
    });

    tearDown(() {
      testBloc.close();
    });

    test('initial state should be TestState with "test"', () {
      expect(testBloc.state, const TestState("test"));
    });

    group('TestEvent', () {
      blocTest<TestBloc, TestState>(
        'should throw exception when TestEvent is added',
        build: () => testBloc,
        act: (bloc) => bloc.add(const TestEvent()),
        errors: () => [isA<Exception>()],
      );
    });

    group('TestCounterEvent', () {
      blocTest<TestBloc, TestState>(
        'should emit new state with counter value when TestCounterEvent is added',
        build: () => testBloc,
        act: (bloc) => bloc.add(const TestCounterEvent(100)),
        expect: () => [const TestState("100")],
      );

      blocTest<TestBloc, TestState>(
        'should handle different counter values',
        build: () => testBloc,
        act: (bloc) {
          bloc.add(const TestCounterEvent(50));
          bloc.add(const TestCounterEvent(200));
        },
        expect: () => [
          const TestState("50"),
          const TestState("200"),
        ],
      );

      blocTest<TestBloc, TestState>(
        'should ignore duplicate counter events due to distinct transformer',
        build: () => testBloc,
        act: (bloc) {
          bloc.add(const TestCounterEvent(100));
          bloc.add(const TestCounterEvent(100)); // Should be ignored
          bloc.add(const TestCounterEvent(200));
        },
        expect: () => [
          const TestState("100"),
          const TestState("200"),
        ],
      );

      blocTest<TestBloc, TestState>(
        'should handle zero and negative counter values',
        build: () => testBloc,
        act: (bloc) {
          bloc.add(const TestCounterEvent(0));
          bloc.add(const TestCounterEvent(-1));
        },
        expect: () => [
          const TestState("0"),
          const TestState("-1"),
        ],
      );
    });

    group('AuthEmailCheck', () {
      blocTest<TestBloc, TestState>(
        'should emit success state when auth check succeeds',
        build: () {
          when(mockAuthCheckMailUseCase.buildUseCase(any))
              .thenAnswer((_) async => TestConstants.testSuccessApiResponse);
          return testBloc;
        },
        act: (bloc) => bloc.add(AuthEmailCheck(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("success")],
        verify: (_) {
          verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should handle auth check failure',
        build: () {
          when(mockAuthCheckMailUseCase.buildUseCase(any))
              .thenAnswer((_) async => TestConstants.testErrorApiResponse);
          return testBloc;
        },
        act: (bloc) => bloc.add(AuthEmailCheck(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("error")],
        verify: (_) {
          verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should handle use case exception',
        build: () {
          when(mockAuthCheckMailUseCase.buildUseCase(any))
              .thenThrow(Exception('Use case error'));
          return testBloc;
        },
        act: (bloc) => bloc.add(AuthEmailCheck(TestConstants.testAuthCheckEmailRequest)),
        errors: () => [isA<Exception>()],
        verify: (_) {
          verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should ignore duplicate auth check events due to distinct transformer',
        build: () {
          when(mockAuthCheckMailUseCase.buildUseCase(any))
              .thenAnswer((_) async => TestConstants.testSuccessApiResponse);
          return testBloc;
        },
        act: (bloc) {
          final request = TestConstants.testAuthCheckEmailRequest;
          bloc.add(AuthEmailCheck(request));
          bloc.add(AuthEmailCheck(request)); // Should be ignored due to distinct
        },
        expect: () => [const TestState("success")],
        verify: (_) {
          // Should only be called once due to distinct transformer
          verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
        },
      );
    });

    group('AuthEmailCheckWithRunCatching', () {
      blocTest<TestBloc, TestState>(
        'should emit success state when auth check with run catching succeeds',
        build: () {
          when(mockAuthCheckMailUseCase.buildUseCase(any))
              .thenAnswer((_) async => TestConstants.testSuccessApiResponse);
          return testBloc;
        },
        act: (bloc) => bloc.add(AuthEmailCheckWithRunCatching(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("success")],
        verify: (_) {
          verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should handle auth check with run catching failure gracefully',
        build: () {
          when(mockAuthCheckMailUseCase.buildUseCase(any))
              .thenThrow(Exception('Network error'));
          return testBloc;
        },
        act: (bloc) => bloc.add(AuthEmailCheckWithRunCatching(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("error")],
        verify: (_) {
          verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
        },
      );
    });

    group('TestError', () {
      blocTest<TestBloc, TestState>(
        'should handle error with run catching and emit success state',
        build: () => testBloc,
        act: (bloc) => bloc.add(const TestError()),
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 3), // Wait for delayed operations
      );
    });

    group('TestErrorWithCatching', () {
      blocTest<TestBloc, TestState>(
        'should handle error with catching',
        build: () => testBloc,
        act: (bloc) => bloc.add(const TestErrorWithCatching()),
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 3), // Wait for delayed operations
      );
    });

    group('Event Equality', () {
      test('TestEvent should be equal to another TestEvent', () {
        const event1 = TestEvent();
        const event2 = TestEvent();
        expect(event1, equals(event2));
      });

      test('TestCounterEvent should be equal when counter values are same', () {
        const event1 = TestCounterEvent(100);
        const event2 = TestCounterEvent(100);
        expect(event1, equals(event2));
      });

      test('TestCounterEvent should not be equal when counter values are different', () {
        const event1 = TestCounterEvent(100);
        const event2 = TestCounterEvent(200);
        expect(event1, isNot(equals(event2)));
      });

      test('AuthEmailCheck should be equal when requests are same', () {
        final request = TestConstants.testAuthCheckEmailRequest;
        final event1 = AuthEmailCheck(request);
        final event2 = AuthEmailCheck(request);
        expect(event1, equals(event2));
      });
    });

    group('State Equality', () {
      test('TestState should be equal when test values are same', () {
        const state1 = TestState("test");
        const state2 = TestState("test");
        expect(state1, equals(state2));
      });

      test('TestState should not be equal when test values are different', () {
        const state1 = TestState("test1");
        const state2 = TestState("test2");
        expect(state1, isNot(equals(state2)));
      });

      test('TestState props should contain test value', () {
        const state = TestState("test");
        expect(state.props, contains("test"));
      });
    });

    group('Bloc Lifecycle', () {
      test('should close properly without errors', () async {
        final bloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        await bloc.close();
        expect(bloc.isClosed, isTrue);
      });

      test('should not emit states after being closed', () async {
        final bloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        await bloc.close();
        
        expect(() => bloc.add(const TestCounterEvent(100)), throwsStateError);
      });
    });
  });
}
