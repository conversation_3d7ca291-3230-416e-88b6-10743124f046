/*
 * Created Date: 5/12/2023 16:01:6
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 28th December 2023 15:30:28
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

class User {
  final int id;
  final String name;
  final String? tag;

  bool get hasTag => tag != null;

  const User({
    required this.id,
    required this.name,
    required this.tag,
  });
}

class UserDto {
  final int id;
  final String name;
  final int age;

  UserDto({
    required this.id,
    required this.name,
    required this.age,
  });
}
